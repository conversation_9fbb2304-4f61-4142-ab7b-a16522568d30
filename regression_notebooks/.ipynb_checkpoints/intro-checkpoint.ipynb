#%%
cali_file_loc = "/usr/gapps/spot/datasets/newdemo/test"
xaxis = "launchday"
metadata_key = "test"
processes_for_parallel_read = 15
initial_regions = ["main"]

import sys

sys.path.append("/usr/gapps/spot/treescape-ven/lib/python3.9/site-packages")
sys.path.append("/usr/gapps/spot/treescape")

import treescape as tr

caliReader = tr.CaliReader( cali_file_loc, processes_for_parallel_read )
#%%
sl = tr.StackedLine()

# sets the X axis variable in the plot
sl.setXAxis(xaxis)

# aggregate values.  possible values are tr.StackedLine.AVG, tr.StackedLine.MIN, tr.StackedLine.MAX, tr.StackedLine.SUM 
sl.setXAggregation(tr.StackedLine.AVG)

# sets initial nodes to plot.  can plot nodes from the same or different levels of drill down.  You may click on the nodes to drill down.
sl.setDrillLevel( initial_regions )

# set the highest value in the plot
#sl.setYMax(3500)

# set the lowest value in the plot
#sl.setYMin(200)

# set the width of the plot
sl.setWidth(1100)

# set the height of the plot
sl.setHeight(250)

alltests = sorted(tsm, key=lambda x: x.metadata[xaxis])

tsm = tr.TreeScapeModel( caliReader )
grapher = tr.MultiLine(alltests)

#%%
for region in initial_regions:
    grapher.plot_sums( xaxis, region )
#%%
for testname in { t.metadata[metadata_key] for t in tsm }:
    print(testname)
    # render each test.  click on "Run Info" button to see flamegraph and metadata
    sl.render([t for t in tsm if t.metadata[metadata_key] == testname])
#%%
