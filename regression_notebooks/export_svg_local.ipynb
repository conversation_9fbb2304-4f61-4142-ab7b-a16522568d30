#%%
cali_file_loc = "/Users/<USER>/datasets/newdemo/test"
xaxis = "launchday"
metadata_key = "test"
processes_for_parallel_read = 15
initial_regions = ["main"]

import sys

sys.path.append("/Users/<USER>/min-venv/lib/python3.9/site-packages")
sys.path.append("/Users/<USER>/treescape")

import treescape as tr

caliReader = tr.CaliReader( cali_file_loc, processes_for_parallel_read )
tsm = tr.TreeScapeModel( caliReader)
#Always be sure to sort your data into some reasonable way. 
alltests = sorted(tsm, key=lambda x: x.metadata[xaxis])

sl = tr.StackedLine()

##
for testname in { t.metadata[metadata_key] for t in tsm }:
    print(testname)
    # render each test.  click on "Run Info" button to see flamegraph and metadata
    all_tests = [t for t in tsm if t.metadata[metadata_key] == testname]
    sl.exportSVG( "/Users/<USER>/svg_imgs/", all_tests,  "launchday", ["TimeIncrement", "LagrangeLeapFrog"], testname )
#%%
import matplotlib.pyplot as plt

plt.figure(figsize=(13, 2), dpi=100)
plt.plot([1, 2, 3], [4, 5, 6])
plt.title("Should be 1000 x 500 pixels (PNG)")
plt.savefig("test_output.svg", dpi=100)
plt.close()

#%%
import hashlib

def spot2_color_hash(text, alpha=0.6):
    reverse_string = text[::-1]
    hash_obj = hashlib.md5(reverse_string.encode())
    hash_hex = hash_obj.hexdigest()
    r = int(hash_hex[12:14], 16)
    g = int(hash_hex[14:16], 16)
    b = int(hash_hex[16:18], 16)
    return f"rgb({r}, {g}, {b}, {alpha})"

v = spot2_color_hash("testing")
print("test: " + v)
#%%
