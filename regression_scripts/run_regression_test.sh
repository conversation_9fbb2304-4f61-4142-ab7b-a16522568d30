#!/bin/bash

# Regression Test Runner for performance_CaliReader.py
# This script ensures the test runs from the correct directory

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TREESCAPE_DIR="$(dirname "$SCRIPT_DIR")"

echo "Running regression test for performance_CaliReader.py..."
echo "Working directory: $TREESCAPE_DIR"
echo

cd "$TREESCAPE_DIR"
python scripts/regression_test_performance_CaliReader.py

exit_code=$?

if [ $exit_code -eq 0 ]; then
    echo
    echo "✅ Regression test PASSED"
else
    echo
    echo "❌ Regression test FAILED"
fi

exit $exit_code
